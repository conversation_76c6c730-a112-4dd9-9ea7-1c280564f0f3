import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON><PERSON>han, MoreThan, Repository, getManager } from 'typeorm';
import { CompanySubscriptionEntity } from './entity/company-subscription.entity';
import { SalesOrderEntity } from '@modules/sales-order/entity/sales-order.entity';
import { createId } from '@paralleldrive/cuid2';
import { generateInvoiceNumber } from '@common/common-helper';
import { Cron, CronExpression } from '@nestjs/schedule';
import { getErrorMessage } from '@common/error';
import moment from 'moment';
import { calculateSubscriptionPrice } from '@common/pricing-helper';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { ChangeSeatCountInputDTO } from './dto/company-subscription.gql.dto';

@Injectable()
export class CompanySubscriptionService extends TypeOrmQueryService<CompanySubscriptionEntity> {
  private readonly logger = new Logger(CompanySubscriptionService.name);
  constructor(
    @InjectRepository(CompanySubscriptionEntity)
    private readonly companySubscriptionRepo: Repository<CompanySubscriptionEntity>
  ) {
    super(companySubscriptionRepo, { useSoftDelete: true });
  }

  /**
   * Change the seat count for a subscription
   * @param input Seat change details
   * @returns The updated subscription
   */
  async changeSeatCount(input: ChangeSeatCountInputDTO): Promise<CompanySubscriptionEntity> {
    try {
      const entityManager = getManager();

      return await entityManager.transaction(async transactionalEntityManager => {
        // Get the current subscription
        const subscription = await transactionalEntityManager.findOne(
          CompanySubscriptionEntity,
          input.subscriptionId,
          { relations: ['subscriptionPackage', 'company'] }
        );

        if (!subscription) {
          throw new NotFoundException(`Subscription with ID ${input.subscriptionId} not found`);
        }

        const currentSeatCount = subscription.seatCount;
        const newSeatCount = input.newSeatCount;

        // No change needed
        if (currentSeatCount === newSeatCount) {
          return subscription;
        }

        // Handle seat addition
        if (newSeatCount > currentSeatCount) {
          // Calculate prorated amount for added seats
          const today = new Date();
          const nextBillingDate = subscription.nextBillingDate || subscription.subscriptionEndDate;

          const addedSeats = newSeatCount - currentSeatCount;

          // Calculate prorated amount for the added seats
          const pricingResult = calculateSubscriptionPrice(
            subscription.subscriptionPackage,
            addedSeats, // Only calculate for the added seats
            subscription.isYearly,
            today
          );

          // Extract the first period amounts (which will be prorated)
          const { baseAmountInCents, sstAmountInCents, totalAmountInCents } = pricingResult.firstPeriod;

          // Create a sales order for the prorated amount
          const salesOrder = new SalesOrderEntity();
          salesOrder.userId = subscription.company.ownerId;
          salesOrder.cuid = createId();
          salesOrder.companyId = subscription.companyId;
          salesOrder.subscriptionPackageId = subscription.subscriptionPackageId;
          salesOrder.total = totalAmountInCents / 100; // Convert cents to RM
          salesOrder.invoiceNumber = generateInvoiceNumber();
          salesOrder.status = 'pending';
          salesOrder.isRecurrence = false;

          // Store seat change details in the data field
          const orderData = {
            prorationType: 'seat-addition',
            currentDate: today,
            nextBillingDate: nextBillingDate,
            previousSeatCount: currentSeatCount,
            newSeatCount: newSeatCount,
            addedSeats: addedSeats,
            baseAmount: baseAmountInCents / 100,
            sstAmount: sstAmountInCents / 100,
            totalAmount: totalAmountInCents / 100,
            isYearly: subscription.isYearly
          };

          salesOrder.data = JSON.stringify(orderData);

          await transactionalEntityManager.save(salesOrder);

          // Return the subscription (seat count will be updated after payment)
          return subscription;
        }
        // Handle seat reduction
        else {
          // Calculate credit for removed seats
          const today = new Date();

          const removedSeats = currentSeatCount - newSeatCount;

          // Calculate prorated credit for the removed seats
          const pricingResult = calculateSubscriptionPrice(
            subscription.subscriptionPackage,
            removedSeats, // Only calculate for the removed seats
            subscription.isYearly,
            today
          );

          // Extract the first period amount (which will be prorated)
          const { totalAmountInCents } = pricingResult.firstPeriod;

          // Calculate credit amount
          const creditAmount = totalAmountInCents / 100; // Convert cents to RM

          // Update the subscription with the new seat count and add credit
          await transactionalEntityManager.update(
            CompanySubscriptionEntity,
            { id: subscription.id },
            {
              seatCount: newSeatCount,
              creditBalance: subscription.creditBalance + creditAmount,
              updatedAt: new Date()
            }
          );

          // Update company maxUsers if needed
          if (subscription.company.maxUsers < newSeatCount) {
            await transactionalEntityManager.update(
              CompanyEntity,
              { id: subscription.companyId },
              { maxUsers: newSeatCount }
            );
          }

          // Return the updated subscription
          return await transactionalEntityManager.findOne(
            CompanySubscriptionEntity,
            input.subscriptionId,
            { relations: ['subscriptionPackage', 'company'] }
          );
        }
      });
    } catch (e) {
      this.logger.error(`Failed to change seat count: ${e.message}`, e.stack);
      throw e;
    }
  }

  /**
   * Check for subscriptions ending soon and create renewal invoices
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async runRecurringBilling(): Promise<void> {
    try {
      const entityManager = getManager();

      const activeSubscriptions = await this.companySubscriptionRepo.find({
        where: {
          subscriptionEndDate: MoreThan(new Date()) && LessThan(new Date(new Date().setDate(new Date().getDate() + 7)))
        },
        relations: ['subscriptionPackage', 'company']
      });

      await entityManager.transaction(async transactionalEntityManager => {
        for (const subscription of activeSubscriptions) {
          const existingPendingSalesOrder = await transactionalEntityManager.findOne(SalesOrderEntity, {
            where: {
              companyId: subscription.companyId,
              subscriptionPackageId: subscription.subscriptionPackageId,
              status: 'pending',
              isRecurrence: true
            }
          });

          if (!existingPendingSalesOrder) {
            const salesOrder = new SalesOrderEntity();
            salesOrder.userId = subscription.company.ownerId;
            salesOrder.cuid = createId();
            salesOrder.companyId = subscription.companyId;
            salesOrder.subscriptionPackageId = subscription.subscriptionPackageId;
            salesOrder.total = subscription.subscriptionPackage.amount;
            salesOrder.invoiceNumber = generateInvoiceNumber();
            salesOrder.status = 'pending';
            salesOrder.isRecurrence = true;

            await transactionalEntityManager.save(salesOrder);
          }
        }
      });
    } catch (e) {
      getErrorMessage(e, 'CompanySubscriptionService', 'runRecurringBilling');
    }
  }

  /**
   * Monthly billing on the 27th of each month
   * Only processes subscriptions for Basic, Advanced, and Business packages
   */
  @Cron('5 0 27 * *', {
    timeZone: 'Asia/Kuala_Lumpur',
  })
  async handleMonthlyBilling() {
    this.logger.log('Starting monthly billing process for Basic, Advanced, and Business packages...');

    try {
      const entityManager = getManager();
      const today = new Date();

      // Define the allowed package titles
      const allowedPackageTitles = ['Basic', 'Advanced', 'Business'];

      // Get active subscriptions with next billing date today or in the past
      // and with subscription packages that match the allowed titles
      const activeSubscriptions = await this.companySubscriptionRepo
        .createQueryBuilder('subscription')
        .innerJoinAndSelect('subscription.subscriptionPackage', 'package')
        .innerJoinAndSelect('subscription.company', 'company')
        .where('subscription.nextBillingDate <= :tomorrow', {
          tomorrow: new Date(new Date().setDate(new Date().getDate() + 1))
        })
        .andWhere('package.title IN (:...allowedTitles)', {
          allowedTitles: allowedPackageTitles
        })
        .getMany();

      this.logger.log(`Found ${activeSubscriptions.length} eligible subscriptions (Basic, Advanced, Business) to bill`);

      await entityManager.transaction(async transactionalEntityManager => {
        for (const subscription of activeSubscriptions) {
          // Skip free trials - they are handled separately
          if (subscription.paymentMethod === 'Free Trial') {
            continue;
          }

          // Log which package is being processed
          this.logger.log(`Processing ${subscription.subscriptionPackage.title} subscription for company ${subscription.company.name}`);

          // Calculate the full amount
          const pricingResult = calculateSubscriptionPrice(
            subscription.subscriptionPackage,
            subscription.seatCount,
            subscription.isYearly
          );

          // Use the full month period for renewal billing
          const { baseAmountInCents, sstAmountInCents, totalAmountInCents } = pricingResult.fullMonthPeriod;

          // Apply any credit balance
          let finalAmount = totalAmountInCents / 100; // Convert cents to RM
          let appliedCredit = 0;

          if (subscription.creditBalance > 0) {
            appliedCredit = Math.min(subscription.creditBalance, finalAmount);
            finalAmount -= appliedCredit;
          }

          // Check for existing pending sales order
          const existingPendingSalesOrder = await transactionalEntityManager.findOne(SalesOrderEntity, {
            where: {
              companyId: subscription.companyId,
              subscriptionPackageId: subscription.subscriptionPackageId,
              status: 'pending',
              isRecurrence: true
            }
          });

          if (!existingPendingSalesOrder) {
            // Create a sales order for the renewal amount
            const salesOrder = new SalesOrderEntity();
            salesOrder.userId = subscription.company.ownerId;
            salesOrder.cuid = createId();
            salesOrder.companyId = subscription.companyId;
            salesOrder.subscriptionPackageId = subscription.subscriptionPackageId;
            salesOrder.total = finalAmount;
            salesOrder.invoiceNumber = generateInvoiceNumber();
            salesOrder.status = 'pending';
            salesOrder.isRecurrence = true;

            // Store billing details in the data field
            const orderData = {
              billingType: 'renewal',
              billingDate: today,
              baseAmount: baseAmountInCents / 100,
              sstAmount: sstAmountInCents / 100,
              totalAmount: totalAmountInCents / 100,
              creditApplied: appliedCredit,
              finalAmount: finalAmount,
              seatCount: subscription.seatCount,
              isYearly: subscription.isYearly
            };

            salesOrder.data = JSON.stringify(orderData);

            await transactionalEntityManager.save(salesOrder);

            // Calculate the next billing date (27th of next month)
            const nextMonth = moment(today).add(1, 'month').date(27).toDate();

            await transactionalEntityManager.update(
              CompanySubscriptionEntity,
              { id: subscription.id },
              {
                nextBillingDate: nextMonth,
                creditBalance: Math.max(0, subscription.creditBalance - appliedCredit),
                updatedAt: new Date()
              }
            );
          }
        }
      });

      this.logger.log('Monthly billing completed successfully');
    } catch (e) {
      this.logger.error('Monthly billing failed', e.stack);
      getErrorMessage(e, 'CompanySubscriptionService', 'handleMonthlyBilling');
    }
  }

  /**
   * Daily check for subscriptions that need renewal (for any that might have been missed on the 27th)
   * Only processes subscriptions for Basic, Advanced, and Business packages
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleMissedRenewals() {
    // Only run this on days other than the 27th
    const today = new Date();
    if (today.getDate() === 27) {
      return;
    }

    this.logger.log('Checking for missed subscription renewals for Basic, Advanced, and Business packages...');

    try {
      const entityManager = getManager();

      // Define the allowed package titles
      const allowedPackageTitles = ['Basic', 'Advanced', 'Business'];

      // Get active subscriptions with next billing date in the past
      // and with subscription packages that match the allowed titles
      const missedSubscriptions = await this.companySubscriptionRepo
        .createQueryBuilder('subscription')
        .innerJoinAndSelect('subscription.subscriptionPackage', 'package')
        .innerJoinAndSelect('subscription.company', 'company')
        .where('subscription.nextBillingDate < :today', { today })
        .andWhere('subscription.paymentMethod != :freeTrialMethod', { freeTrialMethod: 'Free Trial' })
        .andWhere('package.title IN (:...allowedTitles)', {
          allowedTitles: allowedPackageTitles
        })
        .getMany();

      if (missedSubscriptions.length > 0) {
        this.logger.log(`Found ${missedSubscriptions.length} missed subscription renewals for Basic, Advanced, and Business packages`);

        // Process these subscriptions similar to the monthly billing
        await entityManager.transaction(async transactionalEntityManager => {
          for (const subscription of missedSubscriptions) {
            // Log which package is being processed
            this.logger.log(`Processing missed renewal for ${subscription.subscriptionPackage.title} subscription for company ${subscription.company.name}`);

            // Check for existing pending sales order
            const existingPendingSalesOrder = await transactionalEntityManager.findOne(SalesOrderEntity, {
              where: {
                companyId: subscription.companyId,
                subscriptionPackageId: subscription.subscriptionPackageId,
                status: 'pending',
                isRecurrence: true
              }
            });

            if (!existingPendingSalesOrder) {
              // Create a sales order for the renewal
              const pricingResult = calculateSubscriptionPrice(
                subscription.subscriptionPackage,
                subscription.seatCount,
                subscription.isYearly
              );

              // Use the full month period for renewal billing
              const { totalAmountInCents } = pricingResult.fullMonthPeriod;

              const salesOrder = new SalesOrderEntity();
              salesOrder.userId = subscription.company.ownerId;
              salesOrder.cuid = createId();
              salesOrder.companyId = subscription.companyId;
              salesOrder.subscriptionPackageId = subscription.subscriptionPackageId;
              salesOrder.total = totalAmountInCents / 100;
              salesOrder.invoiceNumber = generateInvoiceNumber();
              salesOrder.status = 'pending';
              salesOrder.isRecurrence = true;

              await transactionalEntityManager.save(salesOrder);

              // Update the next billing date to the next 27th
              const nextBillingDate = moment().date(27);
              if (nextBillingDate.isBefore(today)) {
                nextBillingDate.add(1, 'month');
              }

              await transactionalEntityManager.update(
                CompanySubscriptionEntity,
                { id: subscription.id },
                { nextBillingDate: nextBillingDate.toDate() }
              );
            }
          }
        });
      }
    } catch (e) {
      this.logger.error('Missed renewals check failed', e.stack);
      getErrorMessage(e, 'CompanySubscriptionService', 'handleMissedRenewals');
    }
  }
}