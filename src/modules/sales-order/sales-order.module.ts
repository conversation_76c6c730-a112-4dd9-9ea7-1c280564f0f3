import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { SalesOrderEntity } from './entity/sales-order.entity';
import { SalesOrderDto, CreateSalesOrderInputDTO, UpdateSalesOrderInputDTO } from './dto/sales-order.gql.dto';
import { SalesOrderSubscriber } from './sales-order.subscriber';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { SalesOrderService } from './sales-order.service';
import { SalesOrderResolver } from './sales-order.resolver';
import { SalesOrderController } from './sales-order.controller';
import { IntegrationModule } from '@modules/integration/integration.module';
import { SubscriptionPackageModule } from '@modules/subscription-package/subscription-package.module';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([SalesOrderEntity]),
        IntegrationModule,
        SubscriptionPackageModule
      ],
      resolvers: [
        {
          DTOClass: SalesOrderDto,
          EntityClass: SalesOrderEntity,
          CreateDTOClass: CreateSalesOrderInputDTO,
          UpdateDTOClass: UpdateSalesOrderInputDTO,
          create: {
            disabled: true
          },
          update: {
            disabled: true
          },
          delete: {
            disabled: true
          },
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [SalesOrderSubscriber]
    }),
    SubscriptionPackageModule
  ],
  providers: [SalesOrderService, SalesOrderResolver],
  controllers: [SalesOrderController]
})
export class SalesOrderModule {}
