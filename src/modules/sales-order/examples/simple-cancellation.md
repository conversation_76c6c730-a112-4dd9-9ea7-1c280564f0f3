# Simple Sales Order Cancellation

## How It Works

The `SalesOrderSubscriber` now automatically handles cancelled sales orders. When any sales order status is updated to `'cancelled'`, the subscriber will:

1. ✅ Log the cancellation details for audit trail
2. ✅ Identify the type of order (new subscription, seat addition, renewal)
3. ✅ Log appropriate messages for each scenario
4. ✅ Preserve the Billplz bill (allows late payment processing)

## To Cancel a Pending Subscription

Simply update the sales order status to `'cancelled'` using any method:

### Option 1: Direct Database Update
```sql
UPDATE sales_orders 
SET status = 'cancelled', 
    message = 'Cancelled by user',
    data = JSON_SET(data, '$.cancellation', JSON_OBJECT(
        'cancelledAt', NOW(),
        'cancelledBy', 123,
        'reason', 'user_cancelled',
        'originalStatus', 'pending'
    ))
WHERE id = 456 AND status = 'pending';
```

### Option 2: Using TypeORM Repository
```typescript
await getRepository(SalesOrderEntity).update(
  { id: salesOrderId, status: 'pending' },
  {
    status: 'cancelled',
    message: 'Cancelled by user',
    data: JSON.stringify({
      ...existingData,
      cancellation: {
        cancelledAt: new Date().toISOString(),
        cancelledBy: userId,
        reason: 'user_cancelled',
        originalStatus: 'pending'
      }
    })
  }
);
```

### Option 3: Using GraphQL Mutation (if update is enabled)
```graphql
mutation CancelSalesOrder($id: ID!) {
  updateOneSalesOrder(input: {
    id: $id,
    update: {
      status: "cancelled",
      message: "Cancelled by user"
    }
  }) {
    id
    status
    message
  }
}
```

## What Happens Automatically

When the status changes to `'cancelled'`, the subscriber will log:

- **New Subscription**: "Cancelled new subscription order for company X - subscription will not be created"
- **Seat Addition**: "Cancelled seat addition order for company X - Y seats not added"  
- **Renewal**: "Cancelled renewal order for company X - subscription will not be extended"

## Payment Flow Safety

- ✅ Billplz bill remains active
- ✅ If user completes payment later, it will still be processed
- ✅ No disruption to existing payment confirmation workflow
- ✅ Complete audit trail maintained

## Frontend Implementation Example

```typescript
const cancelPendingSubscription = async (salesOrderId: number, userId: number) => {
  try {
    // Update the sales order status - subscriber handles the rest
    await updateSalesOrder({
      variables: {
        id: salesOrderId,
        update: {
          status: 'cancelled',
          message: 'Cancelled by user',
          data: JSON.stringify({
            cancellation: {
              cancelledAt: new Date().toISOString(),
              cancelledBy: userId,
              reason: 'user_cancelled',
              originalStatus: 'pending'
            }
          })
        }
      }
    });
    
    console.log('Subscription cancelled successfully');
    // Subscriber automatically handles logging and cleanup
    
  } catch (error) {
    console.error('Failed to cancel subscription:', error);
  }
};
```

That's it! The subscriber pattern keeps it simple and automatic.
