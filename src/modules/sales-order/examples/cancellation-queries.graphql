# GraphQL queries and mutations for handling pending subscription cancellations

# Get pending sales orders that can be cancelled
query GetCancellablePendingOrders {
  getPendingSalesOrders {
    id
    cuid
    total
    status
    createdAt
    isPendingSubscription
    
    subscriptionPackage {
      title
      amount
    }
    
    # Subscription details for display
    pendingTeamSize
    pendingIsYearly
    pendingProratedAmount
    pendingFullMonthAmount
  }
}

# Cancel a pending subscription
mutation CancelPendingSalesOrder($salesOrderId: Int!) {
  cancelPendingSalesOrder(salesOrderId: $salesOrderId)
}

# Example usage in frontend:
# 1. Query for pending orders to show cancellation options
# 2. Call mutation to cancel specific order
# 3. Refresh pending orders list to update UI

# Frontend implementation example:
# const { data } = useQuery(GET_CANCELLABLE_PENDING_ORDERS);
# const [cancelOrder] = useMutation(CANCEL_PENDING_SALES_ORDER);
# 
# const handleCancel = async (salesOrderId) => {
#   const result = await cancelOrder({ variables: { salesOrderId } });
#   if (result.data.cancelPendingSalesOrder) {
#     // Success - refresh the list
#     refetch();
#   }
# };
