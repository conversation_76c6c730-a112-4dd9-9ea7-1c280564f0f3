import { defaultQueryOptions } from '@constants';
import { Authorize, IDField, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType, Field } from '@nestjs/graphql';
import { SalesOrderEntity } from '../entity/sales-order.entity';
import { SalesOrderAuthorizer } from '../sales-order.authorizer';
import { SubscriptionPackageDto } from '@modules/subscription-package/dto/subscription-package.gql.dto';
import { relationOption } from '@constants/query.constant';
import { CompanyDto } from '@modules/company/dto/company.gql.dto';
import { UserDto } from '@modules/user/dto/user.gql.dto';

@ObjectType('SalesOrder')
@Authorize(SalesOrderAuthorizer)
@Relation('subscriptionPackage', () => SubscriptionPackageDto, relationOption(true))
@Relation('user', () => UserDto, relationOption(true))
@Relation('company', () => CompanyDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class SalesOrderDto extends SalesOrderEntity {}

@InputType()
export class CreateSalesOrderInputDTO {
  @IDField(() => ID) subscriptionPackageId: number;
  @IDField(() => ID) userId: number;
  @IDField(() => ID) companyId: number;
  total: number;
  paymentUrl: string;
}

@InputType()
export class UpdateSalesOrderInputDTO extends PartialType(CreateSalesOrderInputDTO) {
  @Field({ nullable: true })
  status?: string;
}

@ObjectType('SenangPayForm')
export class SenangPayFormDto {
  name: string;
  value: string;
}

@ObjectType('BillplzFormDto')
export class BillplzFormDto {
  @Field(() => String)
  fieldType: string;

  @Field(() => String)
  paymentUrl: string;
}


@ObjectType('PaymentStatus')
export class PaymentStatusDto {
  @Field(() => String)
  status: string;

  @Field(() => String)
  message: string;

  @Field(() => String, { nullable: true })
  orderId?: string;

  @Field(() => String, { nullable: true })
  name?: string;
}

export class BillplzCallbackDto {
  id: string;
  collection_id: string;
  paid: string;
  state: string;
  amount: number;
  paid_amount: number;
  due_at: string;
  email: string;
  mobile: string;
  name: string;
  url: string;
  reference_1_label: string;
  reference_1: string;
  reference_2_label: string;
  reference_2: string;
  redirect_url: string;
  callback_url: string;
  description: string;
  paid_at: string;
  transaction_id: string;
  transaction_status: string;
  x_signature: string;
}

export class BillplzRedirectDto {
  'billplz[id]': string;
  'billplz[paid]': string;
  'billplz[paid_at]': string;
  'billplz[x_signature]': string;
  'billplz[transaction_id]'?: string;
  'billplz[transaction_status]'?: string;
}

@InputType('SubscriptionPurchaseInput')
export class SubscriptionPurchaseInputDto {
  @Field(() => ID)
  subscriptionPackageId: string;

  @Field(() => Number)
  teamSize: number;

  @Field(() => Boolean)
  isYearly: boolean;
}

@ObjectType()
export class BillplzPaymentConfirmationDTO {
  @Field()
  status: string;

  @Field()
  message: string;

  @Field()
  orderId: string;

  @Field()
  name: string

  @Field({ nullable: true })
  transactionId?: string;

  @Field({ nullable: true })
  paidAt?: string;
}
