import { Injectable, Logger } from '@nestjs/common';
import { EventSubscriber, EntitySubscriberInterface, Connection, UpdateEvent, getRepository, MoreThan } from 'typeorm';
import { SalesOrderEntity } from './entity/sales-order.entity';
import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { getErrorMessage } from '@common/error';
import moment from 'moment';

// Define interfaces for order data structure
interface BillingPeriod {
  startDate: string;
  endDate: string;
  amount: number;
  baseAmount: number;
  sstAmount: number;
  isProrated?: boolean;
  label?: string;
  formattedStartDate?: string;
  formattedEndDate?: string;
}

interface BillingDetails {
  firstPeriod: BillingPeriod;
  fullMonthPeriod: BillingPeriod;
  combinedAmount: number;
  combinedBaseAmount: number;
  combinedSstAmount: number;
  subscriptionEndDate: string;
}

interface OrderData {
  // Seat addition properties
  prorationType?: 'seat-addition';
  newSeatCount?: number;
  addedSeats?: number;

  // Renewal properties
  billingType?: 'renewal';

  // Subscription properties
  teamSize?: number;
  isYearly?: boolean;
  originalAmount?: number;
  fullDescription?: string;

  // Billing calculation details
  billingDetails?: BillingDetails;

  // Subscription activation tracking
  subscriptionActivated?: boolean;
  activatedBy?: string;
  activatedAt?: string;
  subscriptionId?: number;

  // Payment details
  payment?: {
    id: string;
    paid: boolean;
    paidAt?: string;
    failedAt?: string;
    transactionId: string;
    paymentMethod: string;
  };

  // Cancellation details
  cancellation?: {
    cancelledAt: string;
    cancelledBy: number;
    reason: string;
    originalStatus: string;
  };
}

@Injectable()
@EventSubscriber()
export class SalesOrderSubscriber implements EntitySubscriberInterface<SalesOrderEntity> {
  private readonly logger = new Logger(SalesOrderSubscriber.name);

  constructor(connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return SalesOrderEntity;
  }

  async afterUpdate(event: UpdateEvent<SalesOrderEntity>) {
    try {
      const { entity } = event;

      if (!entity) {
        return entity;
      }

      // Handle paid orders
      if (entity.status === 'paid') {
        return await this.handlePaidOrder(entity);
      }

      // Handle cancelled orders
      if (entity.status === 'cancelled') {
        return await this.handleCancelledOrder(entity);
      }

      return entity;
    } catch (e) {
      this.logger.error(`Error processing sales order: ${e.message}`, e.stack);
      getErrorMessage(e, 'SalesOrderSubscriber', 'afterUpdate');
    }
  }

  private async handlePaidOrder(entity: any) {

      this.logger.log(`Processing paid sales order: ${entity.id}`);

      const salesOrder = await getRepository(SalesOrderEntity).findOne({
        transactionId: entity.transactionId
      });

      if (!salesOrder) {
        this.logger.error(`Sales order not found for transaction ID: ${entity.transactionId}`);
        return entity;
      }

      // Parse the order data to determine the type of payment
      let orderData: OrderData = {};
      try {
        if (salesOrder.data) {
          orderData = JSON.parse(salesOrder.data) as OrderData;
        }
      } catch (e) {
        this.logger.error(`Error parsing sales order data: ${e.message}`);
      }

      const subscriptionPackage = await getRepository(SubscriptionPackageEntity).findOne({
        id: salesOrder.subscriptionPackageId
      });

      if (!subscriptionPackage) {
        this.logger.error(`Subscription package not found for ID: ${salesOrder.subscriptionPackageId}`);
        return entity;
      }

      // Handle different payment scenarios
      if (orderData.prorationType === 'seat-addition') {
        // This is a payment for adding seats
        const subscription = await getRepository(CompanySubscriptionEntity).findOne({
          where: {
            companyId: salesOrder.companyId,
            subscriptionPackageId: salesOrder.subscriptionPackageId,
            subscriptionEndDate: MoreThan(new Date())
          }
        });

        if (subscription) {
          // Update the subscription with the new seat count
          await getRepository(CompanySubscriptionEntity).update(
            { id: subscription.id },
            {
              seatCount: orderData.newSeatCount,
              updatedAt: new Date()
            }
          );

          // Update company maxUsers if needed
          const company = await getRepository(CompanyEntity).findOne(salesOrder.companyId);
          if (company && company.maxUsers < orderData.newSeatCount) {
            await getRepository(CompanyEntity).update(
              { id: salesOrder.companyId },
              { maxUsers: orderData.newSeatCount }
            );
          }

          this.logger.log(`Subscription ${subscription.id} updated with new seat count: ${orderData.newSeatCount}`);
        } else {
          this.logger.warn(`Active subscription not found for company ${salesOrder.companyId}`);
        }
      }
      else if (orderData.billingType === 'renewal') {
        // This is a regular renewal payment
        const subscription = await getRepository(CompanySubscriptionEntity).findOne({
          where: {
            companyId: salesOrder.companyId,
            subscriptionPackageId: salesOrder.subscriptionPackageId,
            subscriptionEndDate: MoreThan(new Date())
          }
        });

        if (subscription) {
          // Calculate the new subscription end date based on billing period
          const billingPeriod = subscription.isYearly ? 12 : 1; // months

          // First, extend from the current subscription end date
          const extendedEndDate = moment(subscription.subscriptionEndDate).add(billingPeriod, 'months');

          // Then, ensure it aligns with the 27th anchor date
          // Set to the 27th of the month
          const newEndDate = moment(extendedEndDate).date(27);

          // If the original date was after the 27th, move to the next month's 27th
          if (extendedEndDate.date() > 27) {
            newEndDate.add(1, 'month');
          }

          // The next billing date should be the same as the new end date
          // since we've aligned it to the 27th
          const nextBillingDate = moment(newEndDate);

          // Convert Moment objects to JavaScript Date objects
          const newEndDateAsDate = newEndDate.toDate();
          const nextBillingDateAsDate = nextBillingDate.toDate();

          // Log the calculated dates for debugging
          this.logger.log(`Original subscription end date: ${subscription.subscriptionEndDate.toISOString()}`);
          this.logger.log(`Extended by ${billingPeriod} months to: ${extendedEndDate.toDate().toISOString()}`);
          this.logger.log(`Aligned to anchor date (27th): ${newEndDateAsDate.toISOString()}`);
          this.logger.log(`Next billing date set to: ${nextBillingDateAsDate.toISOString()}`);

          await getRepository(CompanySubscriptionEntity).update(
            { id: subscription.id },
            {
              subscriptionEndDate: newEndDateAsDate,
              nextBillingDate: nextBillingDateAsDate,
              paymentMethod: entity.paymentMethod,
              updatedAt: new Date()
            }
          );

          this.logger.log(`Subscription ${subscription.id} renewed until ${newEndDateAsDate}`);
        } else {
          this.logger.warn(`Active subscription not found for company ${salesOrder.companyId}`);
        }
      }
      else {
        // This is a new subscription purchase (or trial conversion)

        // SUBSCRIPTION CREATION DISABLED IN SUBSCRIBER
        // All subscription activations (including new subscriptions and trial conversions)
        // are now handled exclusively by the SubscriptionPackageService.activateSubscription method
        // This prevents duplicate subscriptions from being created and ensures consistent date calculations

        this.logger.log(`Sales order ${salesOrder.id} is for a new subscription or trial conversion.`);
        this.logger.log(`All subscription activations are now handled by the SubscriptionPackageService.activateSubscription method.`);
      }

      return entity;
  }

  private async handleCancelledOrder(entity: any) {
    this.logger.log(`Processing cancelled sales order: ${entity.id}`);

    const salesOrder = await getRepository(SalesOrderEntity).findOne({
      id: entity.id
    });

    if (!salesOrder) {
      this.logger.error(`Sales order not found for ID: ${entity.id}`);
      return entity;
    }

    // Parse the order data to get cancellation details
    let orderData: OrderData = {};
    try {
      if (salesOrder.data) {
        orderData = JSON.parse(salesOrder.data) as OrderData;
      }
    } catch (e) {
      this.logger.error(`Error parsing cancelled sales order data: ${e.message}`);
    }

    // Log cancellation details for audit trail
    if (orderData.cancellation) {
      this.logger.log(`Sales order ${salesOrder.id} cancelled at ${orderData.cancellation.cancelledAt} by user ${orderData.cancellation.cancelledBy} for reason: ${orderData.cancellation.reason}`);
    }

    // If this was a seat addition order, log it
    if (orderData.prorationType === 'seat-addition') {
      this.logger.log(`Cancelled seat addition order for company ${salesOrder.companyId} - ${orderData.addedSeats || orderData.newSeatCount} seats not added`);
    }

    // If this was a renewal order, log it
    if (orderData.billingType === 'renewal') {
      this.logger.log(`Cancelled renewal order for company ${salesOrder.companyId} - subscription will not be extended`);
    }

    // For new subscriptions, just log the cancellation
    if (!orderData.prorationType && !orderData.billingType) {
      this.logger.log(`Cancelled new subscription order for company ${salesOrder.companyId} - subscription will not be created`);
    }

    // Note: We intentionally do NOT cancel the Billplz bill
    // If the user completes payment after cancelling, the payment should still be processed
    if (salesOrder.transactionId) {
      this.logger.log(`Sales order ${salesOrder.id} has Billplz bill ID ${salesOrder.transactionId}. Bill remains active - payment will be processed if completed.`);
    }

    return entity;
  }
}
