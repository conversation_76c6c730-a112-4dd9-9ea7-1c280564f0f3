import { BaseEntity } from '@modules/base/base';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, Field } from '@nestjs/graphql';
import { Entity, Column, ManyToMany, JoinColumn, ManyToOne } from 'typeorm';

@ObjectType()
@Entity('sales_orders')
export class SalesOrderEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  subscriptionPackageId: number;

  @IDField(() => ID)
  @Column('text')
  cuid: string;

  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  companyId: number;

  @FilterableField()
  @Column('float')
  total: number;

  @FilterableField()
  @Column({
    type: 'enum',
    enum: ['pending', 'paid', 'declined', 'inprogress', 'cancelled'],
    default: 'pending'
  })
  status: string;

  @Column('text', { nullable: true })
  transactionId: string;

  @Column('text', { nullable: true })
  message: string;

  @Column('text', { nullable: true })
  hash: string;

  @Field({ nullable: true })
  @Column('text', { nullable: true })
  data: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  paymentMethod: string;

  @FilterableField()
  @Column('text')
  invoiceNumber: string;

  @Column('boolean', { default: false })
  isRecurrence: boolean;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => SubscriptionPackageEntity, subscriptionPackage => subscriptionPackage.salesOrders)
  @JoinColumn({ name: 'subscriptionPackageId' })
  subscriptionPackage: SubscriptionPackageEntity;

  @ManyToOne(() => UserEntity, user => user.salesOrders)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => CompanyEntity, company => company.salesOrders)
  @JoinColumn({ name: 'companyId' })
  company: CompanyEntity;

  @ManyToMany(() => CompanyEntity, company => company.orderTransactions)
  companies: Promise<CompanyEntity[]>;
}
